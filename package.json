{"name": "petconnect-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.25.6", "@headlessui/react": "^2.2.4", "@react-three/drei": "^9.121.2", "@react-three/fiber": "^8.17.12", "aos": "^2.3.4", "axios": "^1.8.4", "country-state-city": "^3.2.1", "daisyui": "^4.12.23", "date-fns": "^4.1.0", "firebase": "^10.14.1", "lodash": "^4.17.21", "marked": "^15.0.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-markdown": "^9.0.3", "react-router-dom": "^6.27.0", "react-select": "^5.10.1", "react-top-loading-bar": "^3.0.2", "reacticon": "^0.0.1", "three": "^0.172.0"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "prettier": "^3.4.2", "tailwindcss": "^3.4.14", "vite": "^5.4.8", "vite-plugin-pwa": "^1.0.0", "vite-plugin-string-replace": "^1.1.3"}}