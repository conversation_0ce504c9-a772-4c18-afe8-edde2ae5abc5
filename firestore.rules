rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /posts/{postID}{
      allow read, write: if request.auth != null 
        || request.resource.data.diff(resource.data).affectedKeys().hasOnly(['likes','dislikes']);
    }
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    match /posts/{postID}/comments/{commentId}{
      allow read, write: if request.auth != null 
        || request.resource.data.diff(resource.data).affectedKeys().hasOnly(['likes','dislikes']);
    }
    match /pets/{petId} {
      allow read, write: if request.auth != null;
    }
    match /chats/{chatId} {
      allow read: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      allow create: if request.auth != null && 
        request.auth.uid in request.resource.data.participants;
      allow update: if request.auth != null && 
        request.auth.uid in resource.data.participants;
    }
    match /messages/{messageId} {
      allow read: if request.auth != null && 
        request.auth.uid in get(/databases/$(database)/documents/chats/$(resource.data.chatId)).data.participants;
      allow create: if request.auth != null && 
        request.auth.uid in get(/databases/$(database)/documents/chats/$(request.resource.data.chatId)).data.participants;
    }
  }
}