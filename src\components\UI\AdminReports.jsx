import React, { useState, useEffect } from "react";
import {
  collection,
  getDocs,
  deleteDoc,
  doc,
  getDoc,
} from "firebase/firestore";
import { db } from "../../firebase/firebase";
import { BiCheck } from "react-icons/bi";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import { useToast } from "../../context/ToastContext";
import { useNotification } from "../../pages/Notification";
import { useNavigate } from "react-router-dom";
import { MdReport } from "react-icons/md";

const AdminReports = () => {
  const [isReportsTableOpen, setIsReportsTableOpen] = useState(false);
  const { showToast } = useToast();
  const [ReportsCount, setReportsCount] = useState(0);
  const [Reportss, setReports] = useState([]);
  const [posts, setPosts] = useState({});
  const { openNotificationModal, NotificationModal } = useNotification();
  const navigate = useNavigate();

  useEffect(() => {
    setReportsCount(Reportss.length);
  }, [Reportss]);

  useEffect(() => {
    const fetchReports = async () => {
      try {
        const ReportsCollection = collection(db, "reports");
        const ReportsSnapshot = await getDocs(ReportsCollection);

        const ReportsList = ReportsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));

        setReports(ReportsList);

        // Fetch posts for each report
        const postsData = {};
        for (const report of ReportsList) {
          if (report.postId) {
            try {
              const postRef = doc(db, "posts", report.postId);
              const postSnapshot = await getDoc(postRef);
              if (postSnapshot.exists()) {
                postsData[report.postId] = postSnapshot.data();
              }
            } catch (error) {
              console.error(`Error fetching post ${report.postId}:`, error);
            }
          }
        }
        setPosts(postsData);
      } catch (error) {
        console.error(error);
      }
    };

    fetchReports();
  }, []);

  const handleCompleteReports = async (id, userId, report) => {
    try {
      // Open modal and wait for result
      const decision = await openActionModal(id, userId, report);
      const success = await openNotificationModal(
        true,
        userId,
        `Your Reports for, "${report}", has been taken care of. Thank you for your Reports!`,
        "reports"
      );

      if (success) {
        await deleteDoc(doc(db, "reports", id));
        setReports((prev) => prev.filter((fb) => fb.id !== id));
        showToast("Reports deleted successfully!");
      }
    } catch (error) {
      console.error("Error deleting Reports:", error);
      showToast("Failed to delete Reports");
    }
  };

  useEffect(() => {
    setIsReportsTableOpen(ReportsCount > 0);
  }, [ReportsCount]);

  const openActionModal = async (id, userId, report) => {
    // Implement your action modal here
    // Return a promise that resolves when the user has made a decision
    // For example:
    return new Promise((resolve) => {
      // Show the modal and wait for user input
      // When the user makes a decision, call resolve with the decision
      // For example:
      resolve(true); // User decided to take action
    });


  return (
    <div className="mt-8">
      <div
        className="flex items-center cursor-pointer bg-base-100 text-base-content py-4 rounded-t border-b-2 border-base-300"
        onClick={() => setIsReportsTableOpen(!isReportsTableOpen)}
      >
        <div className="flex flex-row gap-3 items-center">
          <h2 className="text-2xl font-bold">{"Reports"}</h2>
          {ReportsCount !== 0 && (
            <span className="font-medium text-base flex flex-row gap-2 text-center px-4 py-2 rounded-full bg-base-300">
              <span>{"Pending: "}</span>
              <span>{ReportsCount}</span>
            </span>
          )}
        </div>
        <span className="ml-2">
          <IoIosArrowDown
            size={20}
            className={`transition-all ${isReportsTableOpen ? `-rotate-180` : `rotate-0`}`}
          />
        </span>
      </div>
      <div
        className={`transition-all duration-300 flex flex-row max-sm:flex-col gap-3 mt-4 overflow-auto p-4 ${isReportsTableOpen ? ` inline` : `hidden`}`}
      >
        {ReportsCount === 0 && (
          <span className="font-light italic">{"You're all caught up!"}</span>
        )}
        {Reportss.map((report) => {
          const reportedPost = posts[report.postId];
          return (
            <div
              key={report.id}
              className="w-fit max-sm:w-full min-w-[30%] h-fit p-4 shadow-Uni shadow-base-300 rounded-lg bg-base-100 flex flex-col gap-3"
            >
              <div className="flex justify-between">
                <div className="flex flex-row gap-2 items-center">
                  <div className="flex flex-col">
                    <span className="font-normal">{report.id}</span>
                  </div>
                </div>
              </div>
              <span className="py-4 px-2 rounded-lg bg-base-300 flex flex-row gap-3 items-center">
                <p>{"Report type:"}</p>
                <p className="p-2 bg-base-100 rounded-md">{report.reason}</p>
              </span>
              <span className="py-4 px-2 rounded-lg bg-base-300 flex flex-row gap-3 items-center">
                {report.description}
              </span>
              {reportedPost && (
                <div className="py-4 px-2 rounded-lg bg-base-300 flex flex-col gap-3">
                  <p className="font-semibold">Reported Post:</p>
                  <div
                    className="flex flex-row gap-2 p-2 bg-base-100 w-fit h-fit rounded-full cursor-pointer hover:bg-base-200 transition-colors"
                    onClick={() => {
                      navigate(`/in/profile/${reportedPost.userId}`);
                    }}
                  >
                    <img
                      src={report.profilePic}
                      alt="Profile"
                      className="w-6 h-auto aspect-square object-cover rounded-full"
                    />
                    <p>{reportedPost.handle}</p>
                  </div>
                  <p className="font-medium">{reportedPost.title}</p>
                  <p className="text-sm">{reportedPost.content}</p>
                  {reportedPost.imageUrl && (
                    <>
                      <div className="aspect-video flex justify-center w-full h-[500px] max-sm:h-full overflow-hidden rounded-xl">
                        <img
                          src={reportedPost.imageUrl}
                          alt="Post"
                          className="h-full rounded-none object-contain cursor-pointer "
                        />
                      </div>
                    </>
                  )}
                </div>
              )}
              <div className="flex flex-row gap-2">
                <button
                  className="btn btn-success text-base-100"
                  onClick={() => {
                    handleCompleteReports(
                      report.id,
                      report.userId,
                      report.description
                    );
                  }}
                >
                  <MdReport size={25} />
                  {"Take Action"}
                </button>
              </div>
            </div>
          );
        })}
      </div>
      <NotificationModal />
    </div>
  );
};

export default AdminReports;
