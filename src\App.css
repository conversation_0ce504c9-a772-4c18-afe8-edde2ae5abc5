@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

body.ai-chat{
  overflow: hidden;
}

body {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  
}

/* Add these animation styles */
@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 15px 5px rgba(147, 51, 234, 0.4);
  }
  50% {
    box-shadow: 0 0 25px 10px rgba(147, 51, 234, 0.7);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Add these styles to handle mobile and tablet chat view */
@media (max-width: 1024px) {
  .chat-container {
    margin-left: 0;
    margin-bottom: 0;
  }
  
  /* Prevent content from being hidden behind mobile browser chrome */
  .chat-input-container {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Additional spacing for bottom navigation */
  .content-wrapper {
    padding-bottom: 5rem;
  }
}

/* Ensure proper spacing on larger screens */
@media (min-width: 1025px) {
  .content-wrapper {
    margin-left: 200px;
  }
}

/* Chat and AI Chat specific styles */
.chat-message {
  max-width: 75%;
  word-wrap: break-word;
}

/* Ensure proper spacing for chat input on different devices */
@media (max-width: 1024px) {
  /* Tablet and Mobile adjustments */
  .chat-input-container {
    bottom: 5rem !important; /* Account for bottom navigation */
    margin-left: 0 !important;
  }

  .chat-messages-container {
    margin-bottom: 7rem !important; /* Extra space for input and navigation */
    padding-bottom: 2rem;
  }

  /* Adjust header for better touch targets */
  .chat-header {
    padding: 1rem;
  }

  .chat-header button {
    padding: 0.75rem;
  }

  /* Improve touch targets for message bubbles */
  .message-bubble {
    padding: 0.75rem 1rem;
    margin: 0.5rem 0;
  }
}

/* Desktop specific adjustments */
@media (min-width: 1025px) {
  .chat-input-container {
    margin-left: 200px; /* Account for sidebar */
  }

  .chat-messages-container {
    margin-bottom: 5rem;
  }
}

/* Chat container responsive styles */
.chat-messages-container {
  height: calc(100vh - 9rem);
  max-height: calc(100vh - 9rem);
}

@media (max-width: 1024px) {
  .chat-messages-container {
    height: calc(100vh - 12rem);
    max-height: calc(100vh - 12rem);
  }
}

/* Ensure proper spacing for mobile devices with bottom navigation */
@media (max-width: 768px) {
  .chat-messages-container {
    height: calc(100vh - 14rem);
    max-height: calc(100vh - 14rem);
  }
}

@keyframes textSize {
  0% {
    transform-origin: left;
    transform: scale(5) translateY(1000%);
  }
  50% {
    transform-origin: left;
    transform: scale(5) translateY(0%);
  }
  100% {
    transform-origin: left;
    transform: scale(1) translateY(0%);
  }
}

.animate-textSize{
  animation: textSize 4s ease-in-out;
}

@keyframes subText {
  0% {
    transform-origin: left;
    transform: scale(1) translateY(500%);
    opacity: 0;
  }
  100% {
    transform-origin: left;
    transform: scale(1) translateY(0%);
    opacity: 1;
  }
}

.animate-subText{
  animation: subText 2s ease-in-out;
}